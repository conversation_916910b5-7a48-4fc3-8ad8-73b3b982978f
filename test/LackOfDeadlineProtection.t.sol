// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.0;

import "forge-std/Test.sol";
import "../src/HypoVault.sol";
import {ERC20S} from "lib/panoptic-v1.1/test/foundry/testUtils/ERC20S.sol";
import {IVaultAccountant} from "../src/interfaces/IVaultAccountant.sol";

contract VaultAccountantMock {
    uint256 public nav;
    address public expectedVault;
    bytes public expectedManagerInput;

    function setNav(uint256 _nav) external {
        nav = _nav;
    }

    function setExpectedVault(address _expectedVault) external {
        expectedVault = _expectedVault;
    }

    function setExpectedManagerInput(bytes memory _expectedManagerInput) external {
        expectedManagerInput = _expectedManagerInput;
    }

    function computeNAV(
        address vault,
        address,
        bytes memory managerInput
    ) external view returns (uint256) {
        require(vault == expectedVault, "Invalid vault");
        if (managerInput.length > 0) {
            require(
                keccak256(managerInput) == keccak256(expectedManagerInput),
                "Invalid manager input"
            );
        }
        return nav;
    }
}

contract PoC is Test {
    VaultAccountantMock public accountant;
    HypoVault public vault;
    ERC20S public token;

    address Manager = address(0x1234);
    address FeeWallet = address(0x5678);
    address Alice = address(0x123456);
    address Bob = address(0x12345678);

    uint256 constant INITIAL_BALANCE = 1000000 ether;

    function setUp() public {
        accountant = new VaultAccountantMock();
        token = new ERC20S("Test Token", "TEST", 18);
        vault = new HypoVault(address(token), Manager, IVaultAccountant(address(accountant)), 100);
        accountant.setExpectedVault(address(vault));

        vault.setFeeWallet(FeeWallet);

        address[4] memory users = [Alice, Bob, Manager, FeeWallet];
        for (uint i = 0; i < users.length; i++) {
            token.mint(users[i], INITIAL_BALANCE);
            vm.prank(users[i]);
            token.approve(address(vault), type(uint256).max);
        }
    }

    /// @notice PoC for Lack of Deadline Protection in fulfillWithdrawals()
    /// @dev This test demonstrates how a non-malicious delay in withdrawal
    ///      fulfillment during a market downturn leads to significant user loss.
    function test_submissionValidity() external {
        // --- Step 1: Alice deposits 100 ether and gets shares when NAV is high. ---
        
        // Alice makes her deposit request in epoch 0.
        vm.prank(Alice);
        vault.requestDeposit(100 ether);
        
        // The manager fulfills Alice's deposit promptly.
        vm.startPrank(Manager);
        accountant.setNav(100 ether); // NAV = Alice's deposit
        vault.fulfillDeposits(100 ether, "");
        vault.executeDeposit(Alice, 0);
        vm.stopPrank();
        
        uint256 aliceShares = vault.balanceOf(Alice);
        // At this point, her shares are worth approximately 100 ether.

        // --- Step 2: Alice requests to withdraw all her shares while the NAV is still high. ---
        vm.prank(Alice);
        vault.requestWithdrawal(uint128(aliceShares));
        
        // --- OPERATIONAL DELAY & MARKET CRASH ---
        // A delay occurs (e.g., due to high gas fees).
        // During this delay, the market crashes, and the vault's NAV plummets to 50 ETH.
        accountant.setNav(50 ether);

        // --- STALE FULFILLMENT ---
        // Step 3: Gas fees are now low, so the manager fulfills the stale withdrawal request after the market crash.
        vm.startPrank(Manager);
        
        // The `assetsReceived` will be calculated based on the new, low NAV.
        // `totalAssets` for withdrawal calculation is based on the 50 ETH NAV, which is much lower.
        vault.fulfillWithdrawals(aliceShares, 1000 ether, ""); // Use high maxAssets to pass the internal check.
        
        uint256 aliceBalanceBefore = token.balanceOf(Alice);
        // Her withdrawal request from epoch 0 is now executed.
        vault.executeWithdrawal(Alice, 0); 
        
        uint256 assetsReceived = token.balanceOf(Alice) - aliceBalanceBefore;

        // --- VERIFICATION ---
        // Alice deposited 100 ETH but will get back significantly less due to the delay and market crash.
        // This demonstrates the value loss from the lack of a deadline.
        assertTrue(assetsReceived < 100 ether, "Alice should have received significantly less than her initial deposit");
        
        // A more precise assertion to show the magnitude of the loss.
        // Based on the numbers, her share of the 50 ETH NAV should be much less than her original deposit.
        assertLt(assetsReceived, 75 ether, "Alice received drastically fewer assets due to the fulfillment delay");
        
        vm.stopPrank();
    }
}